<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Fixing Image Display Issues ===\n\n";

// 1. Check if storage symlink exists and works
echo "1. Checking storage symlink...\n";
$storageLink = public_path('storage');
$storageTarget = storage_path('app/public');

if (file_exists($storageLink)) {
    echo "   Storage symlink exists\n";
    if (is_link($storageLink)) {
        echo "   It's a proper symlink\n";
    } else {
        echo "   It's a directory, not a symlink\n";
    }
} else {
    echo "   Storage symlink does not exist\n";
    echo "   Creating storage symlink...\n";
    
    // Try to create symlink
    if (symlink($storageTarget, $storageLink)) {
        echo "   ✓ Storage symlink created successfully\n";
    } else {
        echo "   ✗ Failed to create symlink, trying alternative method...\n";
        
        // For Windows, try using junction
        $cmd = 'mklink /J "' . $storageLink . '" "' . $storageTarget . '"';
        exec($cmd, $output, $return_var);
        
        if ($return_var === 0) {
            echo "   ✓ Windows junction created successfully\n";
        } else {
            echo "   ✗ Failed to create Windows junction\n";
        }
    }
}

// 2. Check image files
echo "\n2. Checking image files...\n";
$imageFiles = [
    'public/images/menu/noodle-beef-special.svg',
    'public/images/menu/noodle-pork-special.svg',
    'public/images/menu/mixed-noodle.svg',
    'public/images/menu/placeholder.svg',
    'public/images/menu/thai-tea.svg',
    'public/images/menu/tofu-fried.svg'
];

foreach ($imageFiles as $file) {
    if (file_exists($file)) {
        echo "   ✓ {$file} exists\n";
    } else {
        echo "   ✗ {$file} missing\n";
    }
}

// 3. Check database menu items
echo "\n3. Checking database menu items...\n";
$menuItems = App\Models\MenuItem::whereNotNull('image')->get();
echo "   Found {$menuItems->count()} menu items with images\n";

$featured = App\Models\MenuItem::where('is_featured', true)->get();
echo "   Found {$featured->count()} featured menu items\n";

foreach ($featured as $item) {
    echo "   - {$item->name}: " . ($item->image ?? 'NO IMAGE') . "\n";
}

// 4. Test ImageHelper
echo "\n4. Testing ImageHelper...\n";
$testPaths = [
    'images/menu/noodle-beef-special.svg',
    'menu-items/test.jpg'
];

foreach ($testPaths as $path) {
    $url = \App\Helpers\ImageHelper::getMenuImageUrl($path);
    echo "   {$path} -> {$url}\n";
}

echo "\n=== Fix Complete ===\n";
