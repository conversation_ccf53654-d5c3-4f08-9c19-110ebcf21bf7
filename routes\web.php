<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\MenuItemController;
use App\Http\Controllers\Admin\NewsController as AdminNewsController;
use App\Http\Controllers\MenuController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\ContactController;
// Controllers ที่ไม่ใช้แล้ว: AdminController, UserController, FontSettingController - ลบออกแล้ว

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Debug route for images
Route::get('/debug-images', function() {
    $menuItems = App\Models\MenuItem::whereNotNull('image')->get(['id', 'name', 'image', 'is_featured']);
    $featured = App\Models\MenuItem::where('is_featured', true)->get(['id', 'name', 'image']);

    $output = "<h1>Debug Images</h1>";

    $output .= "<h2>All Menu Items with Images (" . $menuItems->count() . ")</h2>";
    foreach ($menuItems as $item) {
        $imageUrl = \App\Helpers\ImageHelper::getMenuImageUrl($item->image);
        $output .= "<div style='margin: 10px; padding: 10px; border: 1px solid #ccc;'>";
        $output .= "<h3>ID: {$item->id} - {$item->name}</h3>";
        $output .= "<p>Image Path: {$item->image}</p>";
        $output .= "<p>Generated URL: {$imageUrl}</p>";
        $output .= "<p>Featured: " . ($item->is_featured ? 'YES' : 'NO') . "</p>";
        $output .= "<img src='{$imageUrl}' alt='{$item->name}' style='max-width: 200px; max-height: 200px;'>";
        $output .= "</div>";
    }

    $output .= "<h2>Featured Menu Items (" . $featured->count() . ")</h2>";
    foreach ($featured as $item) {
        if ($item->image) {
            $imageUrl = \App\Helpers\ImageHelper::getMenuImageUrl($item->image);
            $output .= "<div style='margin: 10px; padding: 10px; border: 1px solid #ccc; background: #f0f0f0;'>";
            $output .= "<h3>ID: {$item->id} - {$item->name}</h3>";
            $output .= "<p>Image Path: {$item->image}</p>";
            $output .= "<p>Generated URL: {$imageUrl}</p>";
            $output .= "<img src='{$imageUrl}' alt='{$item->name}' style='max-width: 200px; max-height: 200px;'>";
            $output .= "</div>";
        }
    }

    return $output;
});

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');



// Public Pages
Route::get('/menu', [MenuController::class, 'index'])->name('menu.index');
Route::get('/menu/{id}', [MenuController::class, 'show'])->name('menu.show');
Route::get('/menu/category/{slug}', [MenuController::class, 'category'])->name('menu.category');
Route::get('/menu/{id}', [MenuController::class, 'show'])->name('menu.show');
Route::get('/news', [NewsController::class, 'index'])->name('news.index');
Route::get('/news/{id}', [NewsController::class, 'show'])->name('news.show');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [ContactController::class, 'index'])->name('contact.index');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Test Login Route
Route::get('/test-login', function () {
    return view('test-login');
})->name('test.login');
Route::post('/test-login', [AuthController::class, 'login'])->name('test.login.post');

Route::get('/test-admin', function () {
    return view('test-admin');
})->name('test.admin');

// Debug Routes
Route::get('/debug-auth', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user);
        return redirect()->route('admin.dashboard')->with('success', 'Debug login successful');
    }
    return 'Admin user not found';
})->name('debug.auth');

// Force login route
Route::get('/force-login', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user, true);
        session()->regenerate();
        return redirect('/admin')->with('success', 'Force login successful');
    }
    return 'Admin user not found';
})->name('force.login');

// Direct admin dashboard access
Route::get('/direct-admin', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user, true);
        session()->regenerate();

        // Prepare stats data
        $stats = [
            'categories' => \App\Models\Category::count(),
            'menu_items' => \App\Models\MenuItem::count(),
            'news' => \App\Models\News::count(),
        ];

        return view('admin.simple-dashboard', compact('stats'));
    }
    return 'Admin user not found';
})->name('direct.admin');

// Direct admin access for testing
Route::get('/test-admin-direct', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user, true);
        session()->regenerate();
        return view('admin.dashboard');
    }
    return 'Admin user not found';
})->name('test.admin.direct');

// Auto login route - directly go to admin without login form
Route::get('/auto-admin', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user, true);
        session()->regenerate();
        return redirect()->route('admin.categories.index')->with('success', 'เข้าสู่ระบบสำเร็จ');
    }
    return redirect()->route('home')->with('error', 'ไม่พบผู้ใช้งาน Admin');
})->name('auto.admin');

// Simple login form
Route::get('/simple-login', function () {
    return view('auth.simple-login');
})->name('simple.login');

Route::post('/simple-login', function (\Illuminate\Http\Request $request) {
    $credentials = $request->only('email', 'password');

    if (\Illuminate\Support\Facades\Auth::attempt($credentials, true)) {
        $request->session()->regenerate();

        if (\Illuminate\Support\Facades\Auth::user()->isAdmin()) {
            return redirect('/admin')->with('success', 'เข้าสู่ระบบสำเร็จ');
        }

        return redirect('/')->with('success', 'เข้าสู่ระบบสำเร็จ');
    }

    return back()->withErrors(['email' => 'อีเมลหรือรหัสผ่านไม่ถูกต้อง']);
})->name('simple.login.post');

Route::get('/debug-session', function () {
    return [
        'session_id' => session()->getId(),
        'auth_check' => \Illuminate\Support\Facades\Auth::check(),
        'user' => \Illuminate\Support\Facades\Auth::user(),
        'session_data' => session()->all(),
    ];
})->name('debug.session');

// Simple Login Test
Route::get('/simple-login', function () {
    return view('simple-login');
})->name('simple.login');

Route::post('/simple-login', [AuthController::class, 'login'])->name('simple.login.post');

// Admin Routes
Route::prefix('admin')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/', function () {
        return redirect()->route('admin.categories.index');
    })->name('admin.dashboard');

    // Category Management
    Route::resource('categories', CategoryController::class)->names([
        'index' => 'admin.categories.index',
        'create' => 'admin.categories.create',
        'store' => 'admin.categories.store',
        'show' => 'admin.categories.show',
        'edit' => 'admin.categories.edit',
        'update' => 'admin.categories.update',
        'destroy' => 'admin.categories.destroy',
    ]);

    // Menu Item Management
    Route::resource('menu-items', MenuItemController::class)->names([
        'index' => 'admin.menu-items.index',
        'create' => 'admin.menu-items.create',
        'store' => 'admin.menu-items.store',
        'show' => 'admin.menu-items.show',
        'edit' => 'admin.menu-items.edit',
        'update' => 'admin.menu-items.update',
        'destroy' => 'admin.menu-items.destroy',
    ]);

    // News Management
    Route::resource('news', AdminNewsController::class)->names([
        'index' => 'admin.news.index',
        'create' => 'admin.news.create',
        'store' => 'admin.news.store',
        'show' => 'admin.news.show',
        'edit' => 'admin.news.edit',
        'update' => 'admin.news.update',
        'destroy' => 'admin.news.destroy',
    ]);

    // Routes ที่ไม่ใช้แล้ว: users, restaurant-info, hero-content, about-page, contact-page - ลบออกแล้ว

    // Image Management API
    Route::get('images/api', [App\Http\Controllers\Admin\ImageController::class, 'api'])->name('admin.images.api');
    Route::post('images/upload', [App\Http\Controllers\Admin\ImageController::class, 'upload'])->name('admin.images.upload');
});
