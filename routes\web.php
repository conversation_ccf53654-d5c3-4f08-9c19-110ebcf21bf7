<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\Admin\CategoryController;
use App\Http\Controllers\Admin\MenuItemController;
use App\Http\Controllers\Admin\NewsController as AdminNewsController;
use App\Http\Controllers\MenuController;
use App\Http\Controllers\NewsController;
use App\Http\Controllers\ContactController;
// Controllers ที่ไม่ใช้แล้ว: AdminController, UserController, FontSettingController - ลบออกแล้ว

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Debug route for images
Route::get('/debug-images', function() {
    $menuItems = App\Models\MenuItem::whereNotNull('image')->get(['id', 'name', 'image', 'is_featured']);
    $featured = App\Models\MenuItem::where('is_featured', true)->get(['id', 'name', 'image']);

    $output = "<h1>Debug Images</h1>";

    $output .= "<h2>All Menu Items with Images (" . $menuItems->count() . ")</h2>";
    foreach ($menuItems as $item) {
        $imageUrl = \App\Helpers\ImageHelper::getMenuImageUrl($item->image);
        $output .= "<div style='margin: 10px; padding: 10px; border: 1px solid #ccc;'>";
        $output .= "<h3>ID: {$item->id} - {$item->name}</h3>";
        $output .= "<p>Image Path: {$item->image}</p>";
        $output .= "<p>Generated URL: {$imageUrl}</p>";
        $output .= "<p>Featured: " . ($item->is_featured ? 'YES' : 'NO') . "</p>";
        $output .= "<img src='{$imageUrl}' alt='{$item->name}' style='max-width: 200px; max-height: 200px;'>";
        $output .= "</div>";
    }

    $output .= "<h2>Featured Menu Items (" . $featured->count() . ")</h2>";
    foreach ($featured as $item) {
        if ($item->image) {
            $imageUrl = \App\Helpers\ImageHelper::getMenuImageUrl($item->image);
            $output .= "<div style='margin: 10px; padding: 10px; border: 1px solid #ccc; background: #f0f0f0;'>";
            $output .= "<h3>ID: {$item->id} - {$item->name}</h3>";
            $output .= "<p>Image Path: {$item->image}</p>";
            $output .= "<p>Generated URL: {$imageUrl}</p>";
            $output .= "<img src='{$imageUrl}' alt='{$item->name}' style='max-width: 200px; max-height: 200px;'>";
            $output .= "</div>";
        }
    }

    return $output;
});

// Debug route for restaurant images
Route::get('/debug-restaurant-images', function() {
    $restaurantImagesPath = public_path('images/restaurant');
    $images = [];

    if (is_dir($restaurantImagesPath)) {
        $files = scandir($restaurantImagesPath);
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..' && !is_dir($restaurantImagesPath . '/' . $file)) {
                $images[] = $file;
            }
        }
    }

    $output = "<h1>Restaurant Images Debug</h1>";
    $output .= "<p>Found " . count($images) . " images in public/images/restaurant/</p>";

    foreach ($images as $image) {
        $imagePath = 'images/restaurant/' . $image;
        $imageUrl = \App\Helpers\ImageHelper::getRestaurantImageUrl($imagePath);
        $directUrl = asset($imagePath);

        $output .= "<div style='margin: 20px; padding: 15px; border: 2px solid #ddd; border-radius: 8px;'>";
        $output .= "<h3>" . htmlspecialchars($image) . "</h3>";
        $output .= "<p><strong>File Path:</strong> {$imagePath}</p>";
        $output .= "<p><strong>Helper URL:</strong> {$imageUrl}</p>";
        $output .= "<p><strong>Direct URL:</strong> {$directUrl}</p>";

        // Check if file exists
        $fullPath = public_path($imagePath);
        $exists = file_exists($fullPath);
        $output .= "<p><strong>File Exists:</strong> " . ($exists ? '✅ YES' : '❌ NO') . "</p>";

        if ($exists) {
            $fileSize = filesize($fullPath);
            $output .= "<p><strong>File Size:</strong> " . number_format($fileSize / 1024, 2) . " KB</p>";
        }

        // Display image using both URLs
        $output .= "<div style='display: flex; gap: 20px; margin-top: 10px;'>";
        $output .= "<div>";
        $output .= "<h4>Using Helper URL:</h4>";
        $output .= "<img src='{$imageUrl}' alt='{$image}' style='max-width: 300px; max-height: 200px; border: 1px solid #ccc;' onerror='this.style.border=\"2px solid red\"; this.alt=\"Failed to load: {$imageUrl}\";'>";
        $output .= "</div>";
        $output .= "<div>";
        $output .= "<h4>Using Direct URL:</h4>";
        $output .= "<img src='{$directUrl}' alt='{$image}' style='max-width: 300px; max-height: 200px; border: 1px solid #ccc;' onerror='this.style.border=\"2px solid red\"; this.alt=\"Failed to load: {$directUrl}\";'>";
        $output .= "</div>";
        $output .= "</div>";
        $output .= "</div>";
    }

    return $output;
});

// Restaurant Gallery Route
Route::get('/restaurant-gallery', function() {
    $restaurantImagesPath = public_path('images/restaurant');
    $images = [];

    if (is_dir($restaurantImagesPath)) {
        $files = scandir($restaurantImagesPath);
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..' && !is_dir($restaurantImagesPath . '/' . $file)) {
                // Filter image files only
                $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'])) {
                    $images[] = $file;
                }
            }
        }
    }

    // Sort images by name
    sort($images);

    $html = '<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>แกลเลอรี่ร้านก๋วยเตี๋ยวเรือ</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: "Prompt", sans-serif; background: #f8f9fa; }
        .gallery-item {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            border-radius: 15px;
            overflow: hidden;
        }
        .gallery-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
        }
        .gallery-item img {
            width: 100%;
            height: 250px;
            object-fit: cover;
            transition: transform 0.3s ease;
        }
        .gallery-item:hover img {
            transform: scale(1.1);
        }
        .image-title {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            padding: 15px;
            font-weight: 600;
            text-align: center;
            font-size: 0.9rem;
        }
        .hero-section {
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            color: white;
            padding: 60px 0;
            text-align: center;
        }
        .modal-img {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
        }
    </style>
</head>
<body>
    <div class="hero-section">
        <div class="container">
            <h1 class="display-4 fw-bold mb-3">
                <i class="fas fa-images me-3"></i>แกลเลอรี่ร้านก๋วยเตี๋ยวเรือ
            </h1>
            <p class="lead">ชมภาพบรรยากาศและเมนูอาหารของเรา</p>
            <p class="mb-0">พบ ' . count($images) . ' รูปภาพ</p>
        </div>
    </div>

    <div class="container my-5">
        <div class="row g-4">';

    foreach ($images as $index => $image) {
        $imagePath = 'images/restaurant/' . $image;
        $imageUrl = asset($imagePath);
        $imageName = pathinfo($image, PATHINFO_FILENAME);

        $html .= '
            <div class="col-lg-4 col-md-6 col-sm-12">
                <div class="gallery-item bg-white shadow-lg" data-bs-toggle="modal" data-bs-target="#imageModal" data-image="' . $imageUrl . '" data-title="' . htmlspecialchars($imageName) . '">
                    <img src="' . $imageUrl . '" alt="' . htmlspecialchars($imageName) . '" loading="lazy">
                    <div class="image-title">
                        ' . htmlspecialchars($imageName) . '
                    </div>
                </div>
            </div>';
    }

    $html .= '
        </div>
    </div>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content bg-transparent border-0">
                <div class="modal-header border-0">
                    <h5 class="modal-title text-white" id="modalTitle"></h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="" class="modal-img">
                </div>
            </div>
        </div>
    </div>

    <div class="text-center my-5">
        <a href="/" class="btn btn-primary btn-lg">
            <i class="fas fa-home me-2"></i>กลับหน้าหลัก
        </a>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            const modal = document.getElementById("imageModal");
            const modalImage = document.getElementById("modalImage");
            const modalTitle = document.getElementById("modalTitle");

            modal.addEventListener("show.bs.modal", function(event) {
                const button = event.relatedTarget;
                const imageSrc = button.getAttribute("data-image");
                const imageTitle = button.getAttribute("data-title");

                modalImage.src = imageSrc;
                modalImage.alt = imageTitle;
                modalTitle.textContent = imageTitle;
            });
        });
    </script>
</body>
</html>';

    return $html;
});

// Public Routes
Route::get('/', [HomeController::class, 'index'])->name('home');



// Public Pages
Route::get('/menu', [MenuController::class, 'index'])->name('menu.index');
Route::get('/menu/{id}', [MenuController::class, 'show'])->name('menu.show');
Route::get('/menu/category/{slug}', [MenuController::class, 'category'])->name('menu.category');
Route::get('/menu/{id}', [MenuController::class, 'show'])->name('menu.show');
Route::get('/news', [NewsController::class, 'index'])->name('news.index');
Route::get('/news/{id}', [NewsController::class, 'show'])->name('news.show');
Route::get('/about', [HomeController::class, 'about'])->name('about');
Route::get('/contact', [ContactController::class, 'index'])->name('contact.index');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

// Authentication Routes
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
});

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Test Login Route
Route::get('/test-login', function () {
    return view('test-login');
})->name('test.login');
Route::post('/test-login', [AuthController::class, 'login'])->name('test.login.post');

Route::get('/test-admin', function () {
    return view('test-admin');
})->name('test.admin');

// Debug Routes
Route::get('/debug-auth', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user);
        return redirect()->route('admin.dashboard')->with('success', 'Debug login successful');
    }
    return 'Admin user not found';
})->name('debug.auth');

// Force login route
Route::get('/force-login', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user, true);
        session()->regenerate();
        return redirect('/admin')->with('success', 'Force login successful');
    }
    return 'Admin user not found';
})->name('force.login');

// Direct admin dashboard access
Route::get('/direct-admin', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user, true);
        session()->regenerate();

        // Prepare stats data
        $stats = [
            'categories' => \App\Models\Category::count(),
            'menu_items' => \App\Models\MenuItem::count(),
            'news' => \App\Models\News::count(),
        ];

        return view('admin.simple-dashboard', compact('stats'));
    }
    return 'Admin user not found';
})->name('direct.admin');

// Direct admin access for testing
Route::get('/test-admin-direct', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user, true);
        session()->regenerate();
        return view('admin.dashboard');
    }
    return 'Admin user not found';
})->name('test.admin.direct');

// Auto login route - directly go to admin without login form
Route::get('/auto-admin', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        \Illuminate\Support\Facades\Auth::login($user, true);
        session()->regenerate();
        return redirect()->route('admin.categories.index')->with('success', 'เข้าสู่ระบบสำเร็จ');
    }
    return redirect()->route('home')->with('error', 'ไม่พบผู้ใช้งาน Admin');
})->name('auto.admin');

// Simple login form
Route::get('/simple-login', function () {
    return view('auth.simple-login');
})->name('simple.login');

Route::post('/simple-login', function (\Illuminate\Http\Request $request) {
    $credentials = $request->only('email', 'password');

    if (\Illuminate\Support\Facades\Auth::attempt($credentials, true)) {
        $request->session()->regenerate();

        if (\Illuminate\Support\Facades\Auth::user()->isAdmin()) {
            return redirect('/admin')->with('success', 'เข้าสู่ระบบสำเร็จ');
        }

        return redirect('/')->with('success', 'เข้าสู่ระบบสำเร็จ');
    }

    return back()->withErrors(['email' => 'อีเมลหรือรหัสผ่านไม่ถูกต้อง']);
})->name('simple.login.post');

Route::get('/debug-session', function () {
    return [
        'session_id' => session()->getId(),
        'auth_check' => \Illuminate\Support\Facades\Auth::check(),
        'user' => \Illuminate\Support\Facades\Auth::user(),
        'session_data' => session()->all(),
    ];
})->name('debug.session');

// Simple Login Test
Route::get('/simple-login', function () {
    return view('simple-login');
})->name('simple.login');

Route::post('/simple-login', [AuthController::class, 'login'])->name('simple.login.post');

// Admin Routes
Route::prefix('admin')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/', function () {
        return redirect()->route('admin.categories.index');
    })->name('admin.dashboard');

    // Category Management
    Route::resource('categories', CategoryController::class)->names([
        'index' => 'admin.categories.index',
        'create' => 'admin.categories.create',
        'store' => 'admin.categories.store',
        'show' => 'admin.categories.show',
        'edit' => 'admin.categories.edit',
        'update' => 'admin.categories.update',
        'destroy' => 'admin.categories.destroy',
    ]);

    // Menu Item Management
    Route::resource('menu-items', MenuItemController::class)->names([
        'index' => 'admin.menu-items.index',
        'create' => 'admin.menu-items.create',
        'store' => 'admin.menu-items.store',
        'show' => 'admin.menu-items.show',
        'edit' => 'admin.menu-items.edit',
        'update' => 'admin.menu-items.update',
        'destroy' => 'admin.menu-items.destroy',
    ]);

    // News Management
    Route::resource('news', AdminNewsController::class)->names([
        'index' => 'admin.news.index',
        'create' => 'admin.news.create',
        'store' => 'admin.news.store',
        'show' => 'admin.news.show',
        'edit' => 'admin.news.edit',
        'update' => 'admin.news.update',
        'destroy' => 'admin.news.destroy',
    ]);

    // Routes ที่ไม่ใช้แล้ว: users, restaurant-info, hero-content, about-page, contact-page - ลบออกแล้ว

    // Image Management API
    Route::get('images/api', [App\Http\Controllers\Admin\ImageController::class, 'api'])->name('admin.images.api');
    Route::post('images/upload', [App\Http\Controllers\Admin\ImageController::class, 'upload'])->name('admin.images.upload');
});
