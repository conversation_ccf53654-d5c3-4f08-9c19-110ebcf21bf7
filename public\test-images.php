<?php
// Simple test to check image paths
$imagePaths = [
    'images/menu/noodle-beef-special.svg',
    'images/menu/noodle-pork-special.svg', 
    'images/menu/mixed-noodle.svg',
    'images/menu/placeholder.svg',
    'images/menu/thai-tea.svg',
    'images/menu/tofu-fried.svg'
];

echo "<h1>Image Test</h1>";

foreach ($imagePaths as $path) {
    $fullPath = __DIR__ . '/' . $path;
    $exists = file_exists($fullPath);
    $url = '/' . $path;
    
    echo "<div style='margin: 20px; padding: 10px; border: 1px solid #ccc;'>";
    echo "<h3>Path: {$path}</h3>";
    echo "<p>Full path: {$fullPath}</p>";
    echo "<p>Exists: " . ($exists ? 'YES' : 'NO') . "</p>";
    echo "<p>URL: <a href='{$url}' target='_blank'>{$url}</a></p>";
    
    if ($exists) {
        echo "<img src='{$url}' alt='{$path}' style='max-width: 200px; max-height: 200px;'>";
    }
    
    echo "</div>";
}
?>
