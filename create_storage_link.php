<?php

// Create storage symlink manually
$target = realpath('storage/app/public');
$link = 'public/storage';

// Remove existing link if it exists
if (file_exists($link)) {
    if (is_link($link)) {
        unlink($link);
    } else {
        rmdir($link);
    }
}

// Create the symlink
if (symlink($target, $link)) {
    echo "Storage symlink created successfully!\n";
    echo "Target: {$target}\n";
    echo "Link: {$link}\n";
} else {
    echo "Failed to create storage symlink.\n";
    
    // Try alternative method for Windows
    $cmd = "mklink /D \"" . realpath('public') . "\\storage\" \"" . $target . "\"";
    echo "Trying Windows command: {$cmd}\n";
    
    $output = [];
    $return_var = 0;
    exec($cmd, $output, $return_var);
    
    if ($return_var === 0) {
        echo "Windows symlink created successfully!\n";
    } else {
        echo "Failed to create Windows symlink. Output:\n";
        print_r($output);
    }
}

// Test the link
if (file_exists('public/storage')) {
    echo "Storage link exists and is accessible.\n";
} else {
    echo "Storage link does not exist or is not accessible.\n";
}
