<?php

require 'vendor/autoload.php';
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

echo "=== Testing Image Helper ===\n\n";

// Test the ImageHelper directly
$testPaths = [
    'images/menu/noodle-beef-special.svg',
    'images/menu/noodle-pork-special.svg',
    'images/menu/mixed-noodle.svg',
    'images/menu/placeholder.svg',
    'menu-items/CV2O1XH5y7aXUs4bUf6qHnCuJPK7FHDj1lqmGB0V.jpg'
];

foreach ($testPaths as $path) {
    echo "Testing path: {$path}\n";
    $url = \App\Helpers\ImageHelper::getMenuImageUrl($path);
    echo "Result URL: {$url}\n";
    
    // Check if file exists
    $publicPath = public_path($path);
    $exists = file_exists($publicPath);
    echo "File exists at {$publicPath}: " . ($exists ? 'YES' : 'NO') . "\n";
    
    if (!$exists && !str_starts_with($path, 'images/')) {
        $altPath = public_path('images/' . $path);
        $altExists = file_exists($altPath);
        echo "Alternative path {$altPath}: " . ($altExists ? 'YES' : 'NO') . "\n";
    }
    
    echo "---\n";
}

echo "\n=== Database Menu Items ===\n\n";

// Check what's in the database
$menuItems = App\Models\MenuItem::whereNotNull('image')->get(['id', 'name', 'image', 'is_featured']);
echo "Found " . $menuItems->count() . " menu items with images:\n";

foreach ($menuItems as $item) {
    echo "ID: {$item->id}, Name: {$item->name}, Image: {$item->image}, Featured: " . ($item->is_featured ? 'YES' : 'NO') . "\n";
}

echo "\n=== Featured Menu Items ===\n\n";

$featured = App\Models\MenuItem::where('is_featured', true)->get(['id', 'name', 'image']);
echo "Found " . $featured->count() . " featured menu items:\n";

foreach ($featured as $item) {
    echo "ID: {$item->id}, Name: {$item->name}, Image: " . ($item->image ?? 'NO IMAGE') . "\n";
    if ($item->image) {
        $url = \App\Helpers\ImageHelper::getMenuImageUrl($item->image);
        echo "  Generated URL: {$url}\n";
    }
}
